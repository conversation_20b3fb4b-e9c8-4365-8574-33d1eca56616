// Test script to verify memory-efficient data loading
use candle_core::{Device, Result};
use candle_bert_time_series::batcher::DatasetBatcher;

mod orderbook_dataset;
use orderbook_dataset::{load_and_prepare_orderbook_data, OrderbookDataset};

const SEQUENCE_LENGTH: usize = 128;
const BATCH_SIZE: usize = 8;

fn main() -> Result<()> {
    println!("🧪 Testing Memory-Efficient Data Loading");
    
    let device = Device::Cpu;
    
    // Test with a small dataset first
    let test_paths = vec!["data/orderbook_sample.parquet"];
    
    println!("\n1. Testing data loading...");
    let dataset = load_and_prepare_orderbook_data(&test_paths)?;
    println!("✅ Dataset loaded: {} timesteps × {} features", 
             dataset.num_timesteps, dataset.num_features);
    
    println!("\n2. Testing tensor range extraction...");
    let sample_tensor = dataset.get_tensor_range(0, 10.min(dataset.num_timesteps), &device)?;
    println!("✅ Sample tensor shape: {:?}", sample_tensor.shape());
    
    println!("\n3. Testing batcher...");
    let mut batcher = DatasetBatcher::new(dataset.clone(), SEQUENCE_LENGTH, BATCH_SIZE, false, device.clone())?;
    println!("✅ Batcher created: {} sequences, {} batches", 
             batcher.num_sequences(), batcher.num_batches());
    
    println!("\n4. Testing batch generation...");
    let mut batch_count = 0;
    while let Some(batch_result) = batcher.next() {
        let batch = batch_result?;
        println!("  Batch {}: shape {:?}", batch_count + 1, batch.shape());
        batch_count += 1;
        
        // Only test first few batches
        if batch_count >= 3 {
            break;
        }
    }
    
    println!("\n5. Testing data splits...");
    let total_timesteps = dataset.num_timesteps;
    let train_split = (total_timesteps as f32 * 0.7) as usize;
    let val_split = (total_timesteps as f32 * 0.85) as usize;
    
    let train_data = OrderbookDataset {
        data: dataset.data[0..train_split].to_vec(),
        num_features: dataset.num_features,
        num_timesteps: train_split,
    };
    
    let val_data = OrderbookDataset {
        data: dataset.data[train_split..val_split].to_vec(),
        num_features: dataset.num_features,
        num_timesteps: val_split - train_split,
    };
    
    let test_data = OrderbookDataset {
        data: dataset.data[val_split..].to_vec(),
        num_features: dataset.num_features,
        num_timesteps: total_timesteps - val_split,
    };
    
    println!("✅ Data splits - Train: {}, Val: {}, Test: {}", 
             train_data.num_timesteps, val_data.num_timesteps, test_data.num_timesteps);
    
    println!("\n6. Testing memory efficiency...");
    println!("  Original approach: Would load {} MB into GPU memory immediately", 
             (total_timesteps * dataset.num_features * 4) / (1024 * 1024));
    println!("  New approach: Data stays in CPU memory, only batches go to GPU");
    println!("  Batch memory usage: {} KB per batch", 
             (BATCH_SIZE * SEQUENCE_LENGTH * dataset.num_features * 4) / 1024);
    
    println!("\n✅ All tests passed! Memory-efficient loading is working correctly.");
    
    Ok(())
}
