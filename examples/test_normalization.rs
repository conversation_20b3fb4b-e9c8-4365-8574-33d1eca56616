// test_normalization.rs
// Test the new orderbook-aware normalization strategies

use candle_bert_time_series::normalization::{
    compute_column_stats, create_quantile_strategy, normalize_value,
    create_orderbook_price_strategy, create_orderbook_quantity_strategy,
    preprocess_orderbook_data, validate_normalized_data
};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧪 Testing Orderbook-Aware Normalization");
    
    // Create sample orderbook data that mimics real characteristics
    let mut sample_prices = vec![];
    let mut sample_quantities = vec![];
    
    // Generate realistic price data (tightly clustered around a base price)
    let base_price = 65000.0;
    for i in 0..10000 {
        let noise = (i as f64 * 0.001).sin() * 50.0 + (i as f64 * 0.01).cos() * 20.0;
        sample_prices.push(base_price + noise);
    }
    
    // Generate realistic quantity data (many zeros, some extreme values)
    for i in 0..10000 {
        if i % 5 == 0 {
            sample_quantities.push(0.0); // 20% zeros (missing levels)
        } else if i % 100 == 0 {
            sample_quantities.push(1000.0 + (i as f64 * 0.1).sin() * 500.0); // Some large quantities
        } else {
            sample_quantities.push(10.0 + (i as f64 * 0.05).sin() * 5.0); // Normal quantities
        }
    }
    
    println!("📊 Generated sample data:");
    println!("  Prices: {} values", sample_prices.len());
    println!("  Quantities: {} values", sample_quantities.len());
    
    // Test preprocessing
    let mut prices_clean = sample_prices.clone();
    let mut quantities_clean = sample_quantities.clone();
    
    preprocess_orderbook_data(&mut prices_clean, &mut quantities_clean)?;
    
    // Test old quantile normalization
    println!("\n🔧 Testing old quantile normalization:");
    let old_price_strategy = create_quantile_strategy(&prices_clean);
    let old_quantity_strategy = create_quantile_strategy(&quantities_clean);
    
    let mut old_normalized_prices = vec![];
    let mut old_normalized_quantities = vec![];
    
    for &price in prices_clean.iter().take(1000) {
        old_normalized_prices.push(normalize_value(price, &old_price_strategy));
    }
    
    for &qty in quantities_clean.iter().take(1000) {
        old_normalized_quantities.push(normalize_value(qty, &old_quantity_strategy));
    }
    
    println!("Old quantile normalization results:");
    if let Err(e) = validate_normalized_data(&old_normalized_prices, "Old Price Quantile") {
        println!("❌ {}", e);
    }
    if let Err(e) = validate_normalized_data(&old_normalized_quantities, "Old Quantity Quantile") {
        println!("❌ {}", e);
    }
    
    // Test new orderbook-aware normalization
    println!("\n🔧 Testing new orderbook-aware normalization:");
    let new_price_strategy = create_orderbook_price_strategy(&prices_clean);
    let new_quantity_strategy = create_orderbook_quantity_strategy(&quantities_clean);
    
    let mut new_normalized_prices = vec![];
    let mut new_normalized_quantities = vec![];
    
    for &price in prices_clean.iter().take(1000) {
        new_normalized_prices.push(normalize_value(price, &new_price_strategy));
    }
    
    for &qty in quantities_clean.iter().take(1000) {
        new_normalized_quantities.push(normalize_value(qty, &new_quantity_strategy));
    }
    
    println!("New orderbook-aware normalization results:");
    if let Err(e) = validate_normalized_data(&new_normalized_prices, "New Price Orderbook") {
        println!("❌ {}", e);
    }
    if let Err(e) = validate_normalized_data(&new_normalized_quantities, "New Quantity Orderbook") {
        println!("❌ {}", e);
    }
    
    // Compare statistics
    println!("\n📊 Comparison Summary:");
    let old_price_stats = compute_column_stats(&old_normalized_prices);
    let new_price_stats = compute_column_stats(&new_normalized_prices);
    let old_qty_stats = compute_column_stats(&old_normalized_quantities);
    let new_qty_stats = compute_column_stats(&new_normalized_quantities);
    
    println!("Price normalization:");
    println!("  Old: mean={:.3}, std={:.3}, range=[{:.3}, {:.3}]", 
             old_price_stats.mean, old_price_stats.std, old_price_stats.min, old_price_stats.max);
    println!("  New: mean={:.3}, std={:.3}, range=[{:.3}, {:.3}]", 
             new_price_stats.mean, new_price_stats.std, new_price_stats.min, new_price_stats.max);
    
    println!("Quantity normalization:");
    println!("  Old: mean={:.3}, std={:.3}, range=[{:.3}, {:.3}]", 
             old_qty_stats.mean, old_qty_stats.std, old_qty_stats.min, old_qty_stats.max);
    println!("  New: mean={:.3}, std={:.3}, range=[{:.3}, {:.3}]", 
             new_qty_stats.mean, new_qty_stats.std, new_qty_stats.min, new_qty_stats.max);
    
    // Test zero handling specifically
    println!("\n🔍 Testing zero handling:");
    let zero_normalized_old = normalize_value(0.0, &old_quantity_strategy);
    let zero_normalized_new = normalize_value(0.0, &new_quantity_strategy);
    println!("  Zero quantity - Old: {:.3}, New: {:.3}", zero_normalized_old, zero_normalized_new);
    
    println!("\n✅ Normalization testing completed!");
    
    Ok(())
}
