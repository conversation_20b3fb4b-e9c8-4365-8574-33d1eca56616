// regenerate_one_day.rs
// Regenerate one day of data with new normalization for testing

use std::process::Command;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🔄 Regenerating one day of data with new normalization...");
    
    // Use the download_bybit_orderbook example to regenerate 2025-07-20 data
    let output = Command::new("cargo")
        .args(&[
            "run", 
            "--example", 
            "download_bybit_orderbook", 
            "--", 
            "BTCUSDC", 
            "2025-07-20", 
            "2025-07-20"
        ])
        .output()?;
    
    if output.status.success() {
        println!("✅ Successfully regenerated data for 2025-07-20");
        println!("Output: {}", String::from_utf8_lossy(&output.stdout));
    } else {
        println!("❌ Failed to regenerate data");
        println!("Error: {}", String::from_utf8_lossy(&output.stderr));
        return Err("Data regeneration failed".into());
    }
    
    Ok(())
}
